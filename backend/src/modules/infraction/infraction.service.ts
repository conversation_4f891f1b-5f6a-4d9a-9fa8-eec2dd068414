import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { CreateInfractionDto } from './dto/create-infraction.dto';
import { UpdateInfractionDto } from './dto/update-infraction.dto';
import { PrismaService } from '../../common';
import { Prisma } from '@prisma/client';

@Injectable()
export class InfractionService {
  constructor(private prismaService: PrismaService) {}

  async create(createInfractionDto: CreateInfractionDto) {
    try {
      const infraction = await this.prismaService.infraction.create({
        data: {
          name: createInfractionDto.name,
          description: createInfractionDto.description,
          userId: createInfractionDto.userId,
          data: createInfractionDto.data,
          infractionDate: createInfractionDto.infractionDate,
        },
      });

      return infraction;
    } catch (error) {
      throw new InternalServerErrorException('Error creating infraction');
    }
  }

  async findAll(where?: Prisma.InfractionWhereInput, select?: Prisma.InfractionSelect) {
    return this.prismaService.infraction.findMany({
      where,
      select,
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    const infraction = await this.prismaService.infraction.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        description: true,
        data: true,
        infractionDate: true,
        createdAt: true,
        updatedAt: true,
        user: {
          select: {
            id: true,
            fullName: true,
            phone: true,
            position: {
              select: {
                id: true,
                name: true,
              },
            },
            Organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        InfractionReason: {
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
            file: {
              select: {
                id: true,
                path: true,
              },
            },
          },
        },
      },
    });

    if (!infraction) {
      throw new NotFoundException(`Infraction with ID ${id} does not exist`);
    }
    return infraction;
  }

  update(id: number, updateInfractionDto: UpdateInfractionDto) {
    return `This action updates a #${id} infraction`;
  }

  remove(id: number) {
    return `This action removes a #${id} infraction`;
  }
}
